import React, { useState, useEffect } from 'react'
import { Card, Tag, message, Spin } from 'antd'
import { useParams, useHistory } from 'react-router-dom'
import GroupedTable from '@/components/GroupedTable'
import ICON1 from '@/assets/img/dataDashboard/icon1.png'
import ICON2 from '@/assets/img/dataDashboard/icon2.png'
import ICON3 from '@/assets/img/dataDashboard/icon3.png'
import ICON_TX from '@/assets/img/dataDashboard/icon_tx.png'
import XmIconReact from '@xm/icons-ai/react-svg'
import WithWatermark from '../components/WithWatermark'

import { PROJECT_STATUS_CONFIG } from 'views/dataDashboard/config'

import {
  getProjectRegionList93653,
  getRegionsList93669,
} from '@/apis/dataDashboard'
const DeploymentAnalysis = () => {
  const history = useHistory()
  const [loading, setLoading] = useState(true)
  const [dataSource, setDataSource] = useState([])
  const [regionSummaryInfo, setRegionSummaryInfo] = useState(null)
  const [regions, setRegions] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 999,
    total: 0,
  })
  // 获取path的type和id
  const { type, id } = useParams()
  const queryFields = [
    {
      label: '区域',
      name: 'regionIds',
      type: 'Cascader',
      options: regions,
    },
  ]
  const statusKey = {
    已部署: 'deploy',
    部署中: 'deployIn',
    资源下发中: 'toBeDeployed',
    洽谈中: 'negotiationNum',
  }
  // 政务预警
  const govWarning = {
    '7天预警': 'governmentSevenWarningStatus',
    '15天预警': 'governmentFifteenWarningStatus',
    '30天预警': 'governmentThemoonWarningStatus',
  }
  // 医疗预警
  const medicalWarning = {
    '7天预警': 'medicalSevenWarningStatus',
    '15天预警': 'medicalFifteenWarningStatus',
    '30天预警': 'medicalThemoonWarningStatus',
  }
  // 教育预警
  const educationWarning = {
    '7天预警': 'educationSevenWarningStatus',
    '15天预警': 'educationFifteenWarningStatus',
    '30天预警': 'educationThemoonWarningStatus',
  }
  // 企业预警
  const enterpriseWarning = {
    '7天预警': 'enterpriseSevenWarningStatus',
    '15天预警': 'enterpriseFifteenWarningStatus',
    '30天预警': 'enterpriseThemoonWarningStatus',
  }
  // 预警颜色
  const warningColor = {
    '7天预警': 'rgba(255, 194, 34, 0.2)',
    '15天预警': 'rgba(255, 153, 51, 0.2)',
    '30天预警': 'rgba(255, 85, 85, 0.2)',
  }
  const columns = [
    {
      title: '地市',
      dataIndex: 'areaName',
      key: 'areaName',
      width: 100,
      fixed: 'left',
      render: (_, data) => (
        <div
          className="flex items-center gap-8"
          onClick={() => {
            if (type == '1') {
              handClick(data)
            }
          }}
        >
          <div
            className={`${type == '1' ? 'cursor-pointer underline' : ''}`}
            title={`${type == '1' ? '查看详情' : ''}`}
            style={{
              color: `${data.deployNum == '0' ? '#ff0000' : '#262A30'}`,
            }}
          >
            {data.areaName}
          </div>
        </div>
      ),
    },
    {
      title: '部署情况',
      dataIndex: 'deploymentStatus',
      key: 'deploymentStatus',
      width: 120,
      render: (_, data) => (
        <div className="flex flex-col">
          <div
            className="cursor-pointer underline"
            title="查看详情"
            onClick={() => {
              history.push(
                `/dataDashboard/deploymentAnalysis/${type}/${data.areaId}`,
              )
            }}
          >
            总数：{data.deployNum}
          </div>
          {['已部署', '部署中', '资源下发中', '洽谈中'].map((item) => (
            <div
              key={item}
              className=" px-4px py-2px rounded-4px mt-4 text-13"
              style={{
                backgroundColor: PROJECT_STATUS_CONFIG.STATUS_DATA.find(
                  (x) => x.name === item,
                ).bgColor,
                width: 'fit-content',
              }}
            >
              <span>{item}：</span>
              <span>{data[statusKey[item]]}</span>
            </div>
          ))}
        </div>
      ),
    },
    {
      title: (
        <>
          <div>新增部署</div>{' '}
          <div style={{ color: '#959BA3', fontSize: '12px' }}>近7日</div>
        </>
      ),
      dataIndex: 'sevenDeploy',
      key: 'sevenDeploy',
      width: 120,
      render: (_, data) => (
        <div
          className="cursor-pointer underline"
          title="查看详情"
          onClick={() => {
            history.push(`/dataDashboard/newDeployment/${type}/${data.areaId}`)
          }}
        >
          {data.sevenDeploy}
        </div>
      ),
    },
    {
      title: '开通租户数',
      dataIndex: 'tenantSum',
      key: 'tenantSum',
      width: 100,
    },
    {
      title: '已部署行业',
      dataIndex: 'deployedIndustries',
      key: 'deployedIndustries',
      children: [
        {
          title: '政务',
          dataIndex: 'industryGovernment',
          key: 'industryGovernment',
          width: 120,
          render: (_, data) => (
            <div className="flex flex-col">
              <div
                className={`${type == '2' ? 'cursor-pointer underline' : ''}`}
                title={`${type == '2' ? '查看详情' : ''}`}
                onClick={() => {
                  if (type == '2') {
                    history.push(
                      `/dataDashboard/deploymentSite/${type}/${data.areaId}/0`,
                    )
                  }
                }}
              >
                {data.industryGovernment}
              </div>
              {Object.keys(govWarning).map(
                (item) =>
                  data[govWarning[item]] > 0 && (
                    <div
                      key={item}
                      className={`px-4px py-2px rounded-4px mt-4 text-13`}
                      style={{
                        backgroundColor: warningColor[item],
                        width: 'fit-content',
                      }}
                    >
                      <span>{item}：</span>
                      <span>{data[govWarning[item]]}</span>
                    </div>
                  ),
              )}
            </div>
          ),
        },
        {
          title: '医疗',
          dataIndex: 'industryMedical',
          key: 'industryMedical',
          width: 120,
          render: (_, data) => (
            <div className="flex flex-col">
              <div
                className={`${type == '2' ? 'cursor-pointer underline' : ''}`}
                title={`${type == '2' ? '查看详情' : ''}`}
                onClick={() => {
                  if (type == '2') {
                    history.push(
                      `/dataDashboard/deploymentSite/${type}/${data.areaId}/3`,
                    )
                  }
                }}
              >
                {data.industryMedical}
              </div>
              {Object.keys(medicalWarning).map(
                (item) =>
                  data[medicalWarning[item]] > 0 && (
                    <div
                      key={item}
                      className={`px-4px py-2px rounded-4px mt-4 text-13`}
                      style={{
                        backgroundColor: warningColor[item],
                        width: 'fit-content',
                      }}
                    >
                      <span>{item}：</span>
                      <span>{data[medicalWarning[item]]}</span>
                    </div>
                  ),
              )}
            </div>
          ),
        },
        {
          title: '教育',
          dataIndex: 'industryEducation',
          key: 'industryEducation',
          width: 120,
          render: (_, data) => (
            <div className="flex flex-col">
              <div
                className={`${type == '2' ? 'cursor-pointer underline' : ''}`}
                title={`${type == '2' ? '查看详情' : ''}`}
                onClick={() => {
                  if (type == '2') {
                    history.push(
                      `/dataDashboard/deploymentSite/${type}/${data.areaId}/2`,
                    )
                  }
                }}
              >
                {data.industryEducation}
              </div>
              {Object.keys(educationWarning).map(
                (item) =>
                  data[educationWarning[item]] > 0 && (
                    <div
                      key={item}
                      className={`px-4px py-2px rounded-4px mt-4 text-13`}
                      style={{
                        backgroundColor: warningColor[item],
                        width: 'fit-content',
                      }}
                    >
                      <span>{item}：</span>
                      <span>{data[educationWarning[item]]}</span>
                    </div>
                  ),
              )}
            </div>
          ),
        },
        {
          title: '企业',
          dataIndex: 'industryEnterprise',
          key: 'industryEnterprise',
          width: 120,
          render: (_, data) => (
            <div className="flex flex-col">
              <div
                className={`${type == '2' ? 'cursor-pointer underline' : ''}`}
                title={`${type == '2' ? '查看详情' : ''}`}
                onClick={() => {
                  if (type == '2') {
                    history.push(
                      `/dataDashboard/deploymentSite/${type}/${data.areaId}/1`,
                    )
                  }
                }}
              >
                {data.industryEnterprise}
              </div>
              {Object.keys(enterpriseWarning).map(
                (item) =>
                  data[enterpriseWarning[item]] > 0 && (
                    <div
                      key={item}
                      className={`px-4px py-2px rounded-4px mt-4 text-13`}
                      style={{
                        backgroundColor: warningColor[item],
                        width: 'fit-content',
                      }}
                    >
                      <span>{item}：</span>
                      <span>{data[enterpriseWarning[item]]}</span>
                    </div>
                  ),
              )}
            </div>
          ),
        },
      ],
    },
    {
      title: '城市负责人',
      dataIndex: 'manageUserDto',
      key: 'manageUserDto',
      width: 120,
      render: (activity) => (
        <div>
          <div>{activity && activity.length ? activity[0].userName : '--'}</div>
          <div className="text-[#999] text-12">
            {activity && activity.length ? activity[0].mobile : '--'}
          </div>
        </div>
      ),
    },
  ]
  if (type == '1') {
    columns.splice(1, 0, {
      title: '区县覆盖率',
      dataIndex: 'districtCoverage',
      key: 'districtCoverage',
      width: 160,
      render: (_, data) => (
        <div className="flex items-center gap-8">
          <div className="w-[60px] h-6 bg-[#eee] relative rounded-4 overflow-hidden">
            <div
              className={`absolute h-6 bg-[#2277FF]`}
              style={{
                width: `${data.districtSum ? (data.countyPlatform / data.districtSum) * 100 : 0}%`,
              }}
            ></div>
          </div>
          <div
            className={`${type == '1' ? 'cursor-pointer underline' : ''}`}
            title="查看详情"
            onClick={() => {
              if (type == '2') {
                return
              }
              handClick(data)
            }}
          >
            {' '}
            <span
              style={{
                color: `${data.current == '0' ? '#ff0000' : '#2277FF'}`,
              }}
            >
              {data.countyPlatform}
            </span>
            /{data.districtSum}
          </div>
        </div>
      ),
    })
  }
  const cacheRegionName = ['provinceName', 'cityName', 'countyName', 'townName']
  const handClick = (data) => {
    sessionStorage.setItem(cacheRegionName[type], data.areaName)
    history.push(
      `/dataDashboard/deploymentOverview/${type * 1 + 1}/${data.areaId}`,
    )
  }
  const fetchData = async () => {
    setLoading(true)
    const { data } = await getProjectRegionList93653({ regionId: id })
    console.log('getProjectRegionList', data)
    const { regionList = [], regionSummaryInfo } = data
    console.log('regionSummaryInfo', regionList, regionSummaryInfo)
    setDataSource(regionList)
    setPagination({
      current: 1,
      pageSize: 999,
      total: regionList.length,
    })
    setRegionSummaryInfo(regionSummaryInfo)
    setLoading(false)
  }
  // 平铺的地区数据组装成树状结构
  const buildRegionTree = (regions, parentId = null, level = 0) => {
    // 只构建省和市两级，不包含区县级数据
    // level 0: 省级, level 1: 市级
    if (level > 1) {
      return []
    }

    // 直辖市的编码: 北京(110000), 天津(120000), 上海(310000), 重庆(500000)
    const specialCodes = [110000, 120000, 310000, 500000]

    return regions
      .filter((region) => {
        // 基本过滤：父ID匹配
        const parentMatch = region.parentId === parentId

        // 额外过滤：如果父级是直辖市且当前是level 1，则过滤掉区级单位
        // 直辖市的区县ID通常是6位数，前2位是直辖市代码，后4位不以00结尾
        if (level === 1 && specialCodes.includes(parentId)) {
          // 检查是否为区级单位（ID不以00结尾）
          const isDistrict = !String(region.id).endsWith('00')
          return parentMatch && !isDistrict
        }

        return parentMatch
      })
      .map((region) => ({
        ...region,
        value: region.id,
        children: buildRegionTree(regions, region.id, level + 1),
      }))
  }
  const getRegionsList = async () => {
    try {
      const { data } = await getRegionsList93669()
      console.log('getRegions', data)
      // 平铺的地区数据组装成树状结构,只需要省市

      const treeData = buildRegionTree(data, data[0].parentId, 0)
      console.log('treeData', treeData)
      setRegions(treeData)
    } catch (error) {
      console.error('获取区域列表失败:', error)
    }
  }
  // 处理搜索表单提交
  const handleSearch = (values) => {
    console.log('搜索条件:', values)
    const { regionIds } = values
    const specialCodes = [110000, 120000, 310000, 500000]
    if (regionIds && regionIds.length) {
      history.push(
        `/dataDashboard/deploymentOverview/${regionIds.length == 2 || (specialCodes.includes(regionIds[0]) && regionIds.length == 1) ? '2' : '1'}/${regionIds[regionIds.length - 1]}`,
      )
    } else {
      fetchData({
        ...values,
      })
    }
  }
  useEffect(() => {
    getRegionsList()
  }, [])
  // 组件挂载时加载数据
  useEffect(() => {
    fetchData()
  }, [type, id])
  return (
    <>
      <Spin spinning={loading}>
        {/* 顶部统计卡片区域 */}
        {regionSummaryInfo && (
          <div className="w-full flex mb-16">
            {/* 左侧统计卡片 - 占75%宽度 */}
            <div
              className="flex px-16 pt-22 pb-22 mr-16 bg-#fff rounded-8"
              style={{ width: '75%' }}
            >
              {/* 地市覆盖统计 */}
              <div className="flex items-center" style={{ width: '33.33%' }}>
                <div>
                  <img className="w-44 h-44 mr-12" src={ICON1} alt="地市覆盖" />
                </div>
                <div>
                  <div className="text-20 font-700">
                    <span className="text-#2277FF">
                      {regionSummaryInfo.cityAddressSum}
                    </span>
                    /{regionSummaryInfo.citySum}
                  </div>
                  <div>地市覆盖</div>
                </div>
              </div>
              {/* 已部署总数统计 */}
              <div className="flex items-center" style={{ width: '33.33%' }}>
                <div>
                  <img className="w-44 h-44 mr-12" src={ICON2} alt="地市覆盖" />
                </div>
                <div>
                  <div className="text-20 font-700">
                    {regionSummaryInfo.deploySum}
                  </div>
                  <div>已部署总数</div>
                </div>
              </div>
              {/* 租户总数统计 */}
              <div className="flex items-center" style={{ width: '33.33%' }}>
                <div>
                  <img className="w-44 h-44 mr-12" src={ICON3} alt="地市覆盖" />
                </div>
                <div>
                  <div className="text-20 font-700">
                    {regionSummaryInfo.tenantSum}
                  </div>
                  <div>租户总数</div>
                </div>
              </div>
            </div>
            {/* 右侧联系人卡片 - 占25%宽度 */}
            <div
              className="flex px-16 pt-22 pb-22 bg-#fff rounded-8 items-center"
              style={{ width: '25%' }}
            >
              {/* 联系人头像 */}
              <div>
                <img
                  className="w-44 h-44 mr-12"
                  src={
                    regionSummaryInfo.manageUser.length &&
                    regionSummaryInfo.manageUser[0].avatar
                      ? regionSummaryInfo.manageUser[0].avatar
                      : ICON_TX
                  }
                />
              </div>
              {/* 联系人信息 */}
              <div>
                {/* 联系人姓名和角色 */}
                <div className="font-700 flex items-center">
                  <span className="text-20">
                    {regionSummaryInfo.manageUser.length
                      ? regionSummaryInfo.manageUser[0].userName
                      : '-'}
                  </span>
                  <Tag className="ml-8 h-22 !text-#BE9E6E" color="#f8f5f0">
                    {type == '1' ? '省份负责人' : '地市负责人'}
                  </Tag>
                </div>
                {/* 联系人电话和复制功能 */}
                <div className="flex items-center">
                  <span className="text-#959BA3 text-12">
                    {regionSummaryInfo.manageUser.length &&
                    regionSummaryInfo.manageUser[0].mobile
                      ? regionSummaryInfo.manageUser[0].mobile
                      : '-'}
                  </span>
                  {regionSummaryInfo.manageUser.length > 0 &&
                    regionSummaryInfo.manageUser[0].mobile && (
                      <div
                        className="flex items-center"
                        onClick={() => {
                          {
                            /* 复制电话号码到剪贴板 */
                          }
                          navigator.clipboard
                            .writeText(
                              regionSummaryInfo.manageUser.length
                                ? regionSummaryInfo.manageUser[0].mobile
                                : '-',
                            )
                            .then(() => {
                              message.success('复制成功')
                            })
                            .catch(() => {
                              message.error('复制失败')
                            })
                        }}
                      >
                        <XmIconReact
                          name="replication_line"
                          className="ml-8 cursor-pointer"
                          color="#959BA3"
                          size={16}
                        />
                      </div>
                    )}
                </div>
              </div>
            </div>
          </div>
        )}
        {/* 数据表格卡片容器 */}
        <Card className="relative">
          <GroupedTable
            columns={columns}
            queryFields={queryFields}
            dataSource={dataSource}
            onSearch={handleSearch}
            pagination={false}
            loading={loading}
            fieldAlign="end"
            scroll={{ y: window.innerHeight - 460 }}
            tableProps={{
              rowKey: 'key',
            }}
          />
        </Card>
      </Spin>
    </>
  )
}

// 使用水印高阶组件包装DeploymentAnalysis
export default WithWatermark(DeploymentAnalysis)
