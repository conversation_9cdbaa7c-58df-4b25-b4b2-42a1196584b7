import { defineConfig, presetAttributify, presetUno } from 'unocss'
import transformerVariantGroup from '@unocss/transformer-variant-group'
import transformerDirectives from '@unocss/transformer-directives'
import presetRemToPx from '@unocss/preset-rem-to-px'

export default defineConfig({
  theme: {
    colors: {
      primary: 'var(--xm-primary-color)',
    },
  },
  content: {
    filesystem: ['./src/**/*.{html,js,ts,jsx,tsx}'],
  },
  presets: [
    presetAttributify({
      // antd 的 select 有 size 属性
      ignoreAttributes: ['size'],
    }),
    presetUno(),
    presetRemToPx({ baseFontSize: 4 }),
  ],
  shortcuts: {
    'flex-col': 'flex flex-col',
  },
  variants: [],
  transformers: [transformerVariantGroup(), transformerDirectives()],
  // 添加媒体查询变体
  variants: [
    // 响应式变体
    (matcher) => {
      // sm: 640px, md: 768px, lg: 1024px, xl: 1280px, 2xl: 1536px
      const screens = {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
      }

      const variantEntries = Object.entries(screens)

      for (const [key, value] of variantEntries) {
        if (matcher.startsWith(`${key}:`)) {
          return {
            matcher: matcher.slice(key.length + 1),
            selector: (s) => `@media (min-width: ${value}) { ${s} }`,
          }
        }
      }
    },
  ],
})
