// layout 默认已内置 修改方式参考README.md 自定义layout
// import Layout from '@/layout'
import LayoutPc from '@/layout'
import LayoutH5 from '@/layout/index.h5'

const routes = [
  {
    path: '/',
    redirect: '/clues',
    component: process.env.PUBLIC_H5 ? LayoutH5 : LayoutPc,
    children: [
      {
        path: '/clues',
        exact: true,
        component: () =>
          process.env.PUBLIC_H5
            ? import('/src/views/clues/index.h5')
            : import('/src/views/clues'),
      },
      {
        path: '/clues/create',
        exact: true,
        component: () => import('/src/views/clues/create'),
        meta: {
          title: '上报线索',
          title2: '编辑线索',
        },
      },
      {
        path: '/clues/detail',
        exact: true,
        component: () => import('/src/views/clues/detail'),
        meta: {
          title: '线索详情',
        },
      },
      {
        path: '/demind',
        exact: true,
        component: () =>
          process.env.PUBLIC_H5
            ? import('/src/views/demind/index.h5')
            : import('/src/views/demind'),
      },
      {
        path: '/demind/create',
        exact: true,
        component: () => import('/src/views/demind/create'),
        meta: {
          title: '上报需求',
          title2: '编辑需求',
        },
      },
      {
        path: '/demind/detail',
        exact: true,
        component: () => import('/src/views/demind/detail'),
        meta: {
          title: '需求详情',
        },
      },
      {
        path: '/chat',
        exact: true,
        component: () => import('/src/views/chat'),
      },
      {
        path: '/error/404',
        exact: true,
        component: () => import('/src/views/404'),
      },
      // 部署驾驶舱
      {
        path: '/dataDashboard',
        exact: true,
        component: () => import('/src/views/dataDashboard'),
      },
      // 部署版本分布情况
      {
        path: '/dataDashboard/deploymentVersion',
        exact: true,
        component: () => import('/src/views/dataDashboard/deploymentVersion'),
        meta: {
          title: '部署版本分布情况',
        },
      },
      // 不活跃排行
      {
        path: '/dataDashboard/inactiveRank',
        exact: true,
        component: () => import('/src/views/dataDashboard/inactiveRank'),
        meta: {
          title: '不活跃排行',
        },
      },
      // 新部署情况
      {
        path: '/dataDashboard/newDeployment/:type?/:id?',
        exact: true,
        component: () => import('/src/views/dataDashboard/newDeployment'),
        meta: {
          title: '新部署情况',
        },
      },
      // 部署分析
      {
        path: '/dataDashboard/deploymentAnalysis/:type?/:id?',
        exact: true,
        component: () => import('/src/views/dataDashboard/deploymentAnalysis'),
        meta: {
          title: '部署分析',
        },
      },
      // 地市部署概况
      {
        path: '/dataDashboard/deploymentOverview/:type?/:id?',
        exact: true,
        component: () => import('/src/views/dataDashboard/deploymentOverview'),
        meta: {
          title: '部署概况',
        },
      },
      // 局点部署情况
      {
        path: '/dataDashboard/deploymentSite/:type?/:id?/:industry?',
        exact: true,
        component: () => import('/src/views/dataDashboard/deploymentSite'),
        meta: {
          title: '局点部署情况',
        },
      },
    ],
  },
]

export default routes
