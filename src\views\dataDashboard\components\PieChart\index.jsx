import ReactEcharts from 'echarts-for-react'
import { Badge } from 'antd'
import React, { useMemo } from 'react'
import { PIE_CHART_CONFIG } from 'views/dataDashboard/config'

/**
 * 生成饼图配置选项
 * @param {Array} data - 饼图数据
 * @returns {Object} 饼图配置选项
 */
const getPieChartOption = (data) => {
  // 确保数据是数组且不为空
  const validData = Array.isArray(data) && data.length > 0 ? data : []
  const { COLORS, RADIUS, CENTER, RICH_TEXT } = PIE_CHART_CONFIG

  return {
    tooltip: {
      trigger: 'item',
      className: '[&>div>div]:flex [&>div>div]:item-center',
    },
    legend: {
      show: false,
    },
    series: [
      {
        type: 'pie',
        radius: RADIUS,
        center: CENTER,
        data: validData,
        label: {
          show: validData.length > 0,
          position: 'center',
          formatter: function ({ name, percent }) {
            return `{a|${percent}%}\n{b|${name}}`
          },
          rich: RICH_TEXT,
        },
        emphasis: {
          label: {
            show: true,
            backgroundColor: '#fff',
            borderRadius: 8,
          },
        },
      },
    ],
    color: COLORS,
  }
}

/**
 * 饼图组件
 * @param {Array} data - 饼图数据
 * @param {Function} handleClick - 点击处理函数
 */
const PieChart = ({ data = [], handleClick = () => {} }) => {
  // 确保数据是数组
  const safeData = Array.isArray(data) ? data : []
  const { COLORS } = PIE_CHART_CONFIG

  const chartOption = useMemo(() => {
    return getPieChartOption(safeData)
  }, [safeData])

  return (
    <>
      <ReactEcharts
        className="xl:!h-140px xl:w-120px md:!h-100px md:w-100px"
        option={chartOption}
      />
      <ul className="xl:mt-16 md:mt-8">
        {safeData.map((el, index) => (
          <li
            key={`${el.name}-${index}`}
            onClick={() => {
              handleClick(el.name)
            }}
            className="flex w-full justify-between items-center text-[rgba(0,0,0,.6)] mb-4 text-12 cursor-pointer"
          >
            <Badge
              className="[&_.ant-badge-status-dot]:w-8 [&_.ant-badge-status-dot]:h-8 [&_.ant-badge-status-text]:text-[rgba(0,0,0,.6)] [&_.ant-badge-status-text]:!text-12"
              color={COLORS[index % COLORS.length]}
              text={`${el.name}: `}
            />
            {el.value}
          </li>
        ))}
      </ul>
    </>
  )
}

export default PieChart
