/**
 * 部署驾驶舱配置文件
 * 包含公共配置项、状态颜色、地图配置等
 */

// 轮询间隔配置（毫秒）
export const POLLING_INTERVAL = 1000 * 60

// 项目状态配置
export const PROJECT_STATUS_CONFIG = {
  // 状态类型映射
  STATUS_TYPE_MAP: {
    COMPLETED: '已部署',
    NOT_DEPLOYED: '部署中',
    DEPLOYING: '资源下发中',
    NEGOTIATION: '洽谈中',
  },

  // 状态数据（包含颜色和背景色）
  STATUS_DATA: [
    {
      name: '资源下发中',
      color: '#ff9222',
      bgColor: 'rgba(249, 100, 36, 0.15)',
      statusKey: 'DEPLOYING',
    },
    {
      name: '部署中',
      color: '#FFBB33',
      bgColor: 'rgba(255,187,51,0.15)',
      statusKey: 'NOT_DEPLOYED',
    },
    {
      name: '已部署',
      color: '#15D888',
      bgColor: 'rgba(21,216,136,0.15)',
      statusKey: 'COMPLETED',
    },
    {
      name: '洽谈中',
      color: '#2277FF',
      bgColor: 'rgba(34,119,255,0.15)',
      statusKey: 'NEGOTIATION',
    },
  ],
}

// 地图配置
export const MAP_CONFIG = {
  // 省份颜色配置（根据项目数量）
  PROVINCE_COLOR: {
    LEVEL_1: { max: 10, color: '#f7feff' },
    LEVEL_2: { min: 31, max: 60, color: '#f7feff' },
    LEVEL_3: { min: 61, max: 101, color: '#c0deff' },
    LEVEL_4: { min: 101, max: 200, color: '#99c5ff' },
    LEVEL_5: { min: 201, color: '#3788f4' },
  },

  // 地图样式配置
  AREA_ITEM_STYLE: {
    normal: {
      borderColor: '#BDDDF7',
      borderWidth: 0.75,
      areaColor: 'white',
    },
    emphasis: {
      borderColor: '#BDDDF7',
      borderWidth: 0.75,
      areaColor: 'white',
    },
  },

  // 地图缩放和位置配置
  MAP_LAYOUT: {
    aspectScale: 0.8,
    zoom: 1.23,
    layoutCenter: ['50%', '50%'],
    layoutSize: '100%',
  },

  // 散点图效果配置
  SCATTER_EFFECT: {
    symbolSize: 12,
    period: 2,
    scale: 4,
    brushType: 'fill',
  },
}

// 菜单配置
export const DASHBOARD_MENU = [
  { name: '部署版本分布情况', path: '/dataDashboard/deploymentVersion' },
  { name: '不活跃排行', path: '/dataDashboard/inactiveRank' },
  { name: '新部署情况', path: '/dataDashboard/newDeployment' },
  {
    name: 'AB类需求&Bug',
    path: '/dataDashboard/deploymentSite',
    disabled: true,
  },
  { name: '部署分析', path: '/dataDashboard/deploymentAnalysis' },
]

// 饼图配置
export const PIE_CHART_CONFIG = {
  COLORS: ['#4574FF', '#3DCCE1', '#15D888'],
  RADIUS: ['65%', '90%'],
  CENTER: ['50%', '55%'],
  RICH_TEXT: {
    a: {
      fontSize: 16,
      color: '#2277FF',
      fontWeight: 'bold',
      lineHeight: 20,
      padding: [10, 0, 0, 0],
    },
    b: {
      fontSize: 10,
      color: 'rgba(0, 0, 0, 0.5)',
      padding: [5, 0, 0, 0],
    },
  },
}

// 获取省份颜色的工具函数
export const getProvinceColor = (value) => {
  const { PROVINCE_COLOR } = MAP_CONFIG

  if (value <= PROVINCE_COLOR.LEVEL_1.max) return PROVINCE_COLOR.LEVEL_1.color
  if (value <= PROVINCE_COLOR.LEVEL_2.max) return PROVINCE_COLOR.LEVEL_2.color
  if (value <= PROVINCE_COLOR.LEVEL_3.max) return PROVINCE_COLOR.LEVEL_3.color
  if (value <= PROVINCE_COLOR.LEVEL_4.max) return PROVINCE_COLOR.LEVEL_4.color
  return PROVINCE_COLOR.LEVEL_5.color
}
