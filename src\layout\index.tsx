import {
  PageContainer,
  ProCard,
  ProConfigProvider,
  ProLayout,
} from '@ant-design/pro-components'
import { Button, ConfigProvider, LayoutProps } from 'antd'
import React, { useEffect, useState } from 'react'
import defaultProps from './_defaultProps.tsx'
import {
  legacyLogicalPropertiesTransformer,
  StyleProvider,
} from '@ant-design/cssinjs'
import zhCN from 'antd/locale/zh_CN'
import logo from './logo.png'
import XmIconReact from '@xm/icons-ai/react-svg'
import { useHistory } from 'react-router-dom'
import chatIcon from '@/assets/img/chat.png'
// @ts-ignore
import routerConfig from '@/router'
import { useGlobalState } from '@xm/xapp-main'
import { isPc } from 'utils'
// @ts-ignore
import { getFullLocationName, getLocationInfo } from '@/utils/locationUtils'

// 常量定义
const DEPLOYMENT_PATHS = [
  'deploymentOverview',
  'deploymentAnalysis',
  'newDeployment',
  'deploymentSite',
  'deploymentIndustry',
] as const

const LOCATION_TYPES = {
  PROVINCE: 'province',
  CITY: 'city',
} as const

// 辅助函数：检查是否为部署相关路径
const isDeploymentPath = (path: string): boolean => {
  return DEPLOYMENT_PATHS.some((deploymentPath) =>
    path.includes(deploymentPath),
  )
}

// 辅助函数：检查是否为站点或概览路径
const isSiteOrOverviewPath = (path: string): boolean => {
  return path.includes('deploymentSite') || path.includes('deploymentOverview')
}

// 辅助函数：处理位置信息面包屑
const processLocationBreadcrumb = (
  pathname: string,
  item: any,
  meta: any,
): Array<{ breadcrumbName: string; linkPath?: string }> => {
  const append: Array<{ breadcrumbName: string; linkPath?: string }> = []
  const match = pathname.match(/\/(\w+)\/(\w+)$/)

  if (!match) return append

  const id = item?.path.includes('deploymentSite') ? match[1] : match[2]
  const locationName = getLocationInfo(id)

  console.log('locationName', locationName)

  if (isSiteOrOverviewPath(item?.path)) {
    Object.keys(locationName).forEach((obj: any) => {
      if (locationName[obj].name) {
        if (obj === LOCATION_TYPES.PROVINCE || obj === LOCATION_TYPES.CITY) {
          append.push({
            breadcrumbName: `${locationName[obj].name}部署情况`,
            linkPath: `/portal/#/portal/business-sale/dataDashboard/deploymentOverview/${obj === LOCATION_TYPES.PROVINCE ? 1 : 2}/${locationName[obj].code}`,
          })
        } else {
          append.push({
            breadcrumbName: `${locationName[obj].name}${meta.title}`,
          })
        }
      }
    })
  } else {
    const fullLocationName = getFullLocationName(id)
    const breadcrumbName = fullLocationName
      ? `${fullLocationName}${meta.title}`
      : meta.title
    append.push({
      breadcrumbName: breadcrumbName,
      linkPath: `/portal/#/portal/business-sale/dataDashboard/deploymentAnalysis/${id}`,
    })
  }

  return append
}

// 辅助函数：处理查询ID面包屑
const processQueryIdBreadcrumb = (
  meta: any,
  item: any,
): Array<{ breadcrumbName: string; linkPath?: string }> => {
  const breadcrumbName = meta.title2 || meta.title
  return [
    {
      breadcrumbName,
      linkPath: item?.path,
    },
  ]
}

// 主要面包屑处理函数
const generateBreadcrumbAppend = (
  pathname: string,
  item: any,
  meta: any,
  query: { id: string | null },
): Array<{ breadcrumbName: string; linkPath?: string }> => {
  // 如果没有meta.title，直接返回空数组
  if (!meta?.title) {
    return []
  }

  // 检查是否为部署相关路径
  if (isDeploymentPath(item?.path)) {
    return processLocationBreadcrumb(pathname, item, meta)
  }

  // 检查是否有查询ID
  if (query.id) {
    return processQueryIdBreadcrumb(meta, item)
  }

  return []
}

// 辅助函数：处理菜单项点击
const handleMenuItemClick = (
  item: any,
  globalState: any,
  setPathname: (path: string) => void,
  history: any,
) => {
  // 处理文档路径的特殊逻辑
  if (item.path === '/doc') {
    const path =
      location.origin +
      `/portal/#/portal/web-clouddisk/newCloudDriver/company/${globalState.loginInfo.orgId}/4/0/0`

    if (isPc()) {
      const { JSSDK } = window.WEB_UTIL || {}
      JSSDK?.call('system', 'openNewTab', {
        url: path,
        title: '云盘',
        iconPath: '',
        parameter: undefined,
      })
    } else {
      window.open(path)
    }
    return
  }

  // 默认导航逻辑
  setPathname(item.path || '/')
  history.push(item.path || '/')
}

// 辅助函数：渲染头部标题
const renderHeaderTitle = (
  logo: React.ReactNode,
  title: React.ReactNode,
  props: any,
) => {
  const defaultDom = (
    <a>
      {logo}
      {title}
    </a>
  )

  // 早期返回模式，减少嵌套
  if (typeof window === 'undefined') return defaultDom
  if (document.body.clientWidth < 1400) return defaultDom
  if (props.isMobile) return defaultDom

  return <>{defaultDom}</>
}

// 辅助函数：处理返回操作
const handleGoBack = (history: any) => {
  // 如果有历史记录，则返回上一页
  if (window.history.length > 1) {
    history.goBack()
  } else {
    // 如果没有历史记录，返回首页
    history.push('/clues')
  }
}

// 辅助函数：渲染操作按钮
const renderActions = (props: any) => {
  // 早期返回模式，减少嵌套
  if (props.isMobile) return []
  if (typeof window === 'undefined') return []

  return [
    <Button
      type="default"
      className="mr-4 border-0 shadow-none bg-#F7F8F9"
      onClick={() => {
        location.hash = '#/portal/business-sale/chat'
      }}
    >
      <img src={chatIcon} width={18} alt="" />
      <span
        style={{
          // 字体颜色线性渐变
          background: 'linear-gradient(90deg, #3087FF 0%, #F389FF 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        }}
      >
        智能问答
      </span>
    </Button>,
    <Button
      type="primary"
      className="gap-4 line-height-normal"
      icon={
        <XmIconReact
          className="!flex"
          color="#fff"
          size={16}
          name="plus_linear_line"
        />
      }
      onClick={() => {
        location.hash = '#/portal/business-sale/clues/create'
      }}
    >
      上报线索
    </Button>,
  ]
}

interface ILayoutProps extends LayoutProps {
  children: React.ReactNode
}

function Wrapper({ children }: { children: JSX.Element }) {
  const chromeVersion = navigator.userAgent.match(/chrome\/(\d+)/i)
  const isLowerChrome = chromeVersion ? Number(chromeVersion[1]) < 89 : false
  window.isLowerChrome = isLowerChrome

  return isLowerChrome ? (
    <StyleProvider
      hashPriority="high"
      transformers={[legacyLogicalPropertiesTransformer]}
    >
      {children}
    </StyleProvider>
  ) : (
    children
  )
}

export default (props: ILayoutProps) => {
  const [pathname, setPathname] = useState('')
  const [query, setQuery] = useState({} as { id: string | null })
  const history = useHistory()
  const globalState = useGlobalState()

  useEffect(() => {
    const _path = location.hash
      .split('?')[0]
      .split('/')
      .filter(Boolean)
      .slice(3)
      .join('/')
    setPathname(`/${_path}`)

    const _query = location.hash.split('?')[1]
    if (_query) {
      const query = new URLSearchParams(_query)
      setQuery({
        id: query.get('id'),
      })
    } else {
      setQuery({
        id: null,
      })
    }
  }, [location.hash])

  if (typeof document === 'undefined') {
    return <div />
  }
  return (
    <div
      id="test-pro-layout"
      style={{
        transform: 'translateZ(0)',
      }}
      className="h-full overflow-hidden md:overflow-x-auto"
    >
      <ProConfigProvider hashed={false}>
        {/*<Wrapper>*/}
        <ConfigProvider
          locale={zhCN}
          prefixCls="business-ant"
          getTargetContainer={() => {
            return document.getElementById('test-pro-layout') || document.body
          }}
          theme={{ token: { colorPrimary: '#FF9222' } }}
        >
          <ProLayout
            title=""
            logo={logo}
            prefixCls="my-prefix"
            {...defaultProps}
            location={{
              pathname,
            }}
            token={{
              header: {
                colorBgHeader: '#fff',
                // colorBgMenuItemSelected: 'rgba(0,0,0,0.04)',
              },
              pageContainer: {
                paddingBlockPageContainerContent: 0,
              },
            }}
            siderMenuType="group"
            menu={{
              collapsedShowGroupTitle: true,
            }}
            actionsRender={renderActions}
            headerTitleRender={(logo, title, props) =>
              renderHeaderTitle(logo, title, props)
            }
            onMenuHeaderClick={(e) => console.log(e)}
            breadcrumbRender={(routes) => {
              const arr = routerConfig[0].children
              const item = arr.find(
                (item: {
                  path: string
                  meta: { title: string; title2: string; title3: string }
                }) => {
                  if (item.path.includes(':')) {
                    const pathPattern = item.path
                      .replace(/:[^/]+/g, '[^/]+')
                      .replace(/\//g, '\\/')

                    const regex = new RegExp(`^${pathPattern}$`)
                    return regex.test(pathname)
                  }

                  return item.path === pathname
                },
              )

              const meta = item?.meta
              const append = generateBreadcrumbAppend(
                pathname,
                item,
                meta,
                query,
              )

              return routes
                ?.map((route) => {
                  // @ts-ignore
                  route.linkPath = `/portal/#/portal/business-sale${route.linkPath}`
                  return route
                })
                .concat(append)
            }}
            breadcrumbProps={{
              separator: (
                <XmIconReact
                  className="inline-flex"
                  size={14}
                  name="right_line"
                />
              ),
              itemRender: (route, _, routes) => {
                const isFirst = routes.indexOf(route) === 0
                const isLast = routes.indexOf(route) === routes.length - 1
                return (
                  <span className="flex items-center ">
                    {/* 在第一个面包屑项前添加返回按钮 */}
                    {isFirst && (
                      <Button
                        type="text"
                        size="small"
                        className="mr-8 p-4px h-auto border-0 shadow-none bg-white underline hover:!bg-none"
                        onClick={() => handleGoBack(history)}
                      >
                        返回
                      </Button>
                    )}
                    {/* 面包屑项内容 */}
                    {isLast ? (
                      <span className="">
                        {route.title || route.breadcrumbName}
                      </span>
                    ) : route.linkPath ? (
                      <a
                        href={route.linkPath}
                        className="hover:bg-none !text-#262a30"
                      >
                        {route.title || route.breadcrumbName}
                      </a>
                    ) : (
                      <span className=" ">
                        {route.title || route.breadcrumbName}
                      </span>
                    )}
                  </span>
                )
              },
            }}
            menuItemRender={(item, dom) => {
              return (
                <div
                  onClick={() =>
                    handleMenuItemClick(item, globalState, setPathname, history)
                  }
                >
                  {dom}
                </div>
              )
            }}
          >
            <PageContainer
              token={{
                paddingInlinePageContainerContent: 0,
              }}
              // extra={[
              //   <Button key="3">操作</Button>,
              //   <Button key="2">操作</Button>,
              //   <Button
              //     key="1"
              //     type="primary"
              //     onClick={() => {
              //       setNum(num > 0 ? 0 : 40)
              //     }}
              //   >
              //     主操作
              //   </Button>,
              // ]}
              subTitle=""
              // footer={[
              //   <Button key="3">重置</Button>,
              //   <Button key="2" type="primary">
              //     提交
              //   </Button>,
              // ]}
            >
              <ProCard
                style={
                  {
                    // height: '200vh',
                    // minHeight: 800,
                    // height: 'calc(100vh - 60px - 55px - 25px)',
                    // overflow: 'auto',
                  }
                }
                bodyStyle={{
                  paddingTop: 0,
                  marginTop: 16,
                  overflow: 'auto',
                }}
                bordered={false}
                className="bg-[#F7F8F9]"
              >
                {props.children}
              </ProCard>
            </PageContainer>
          </ProLayout>
        </ConfigProvider>
        {/*</Wrapper>*/}
      </ProConfigProvider>
    </div>
  )
}
